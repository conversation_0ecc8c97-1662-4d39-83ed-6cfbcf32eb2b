# Application de Suivi du Temps de Production - Excalibur ERP

## Description

Cette application Streamlit autonome permet de suivre et analyser les temps de production en se connectant directement à la base de données Excalibur ERP. Elle offre une interface web moderne pour visualiser les ordres de fabrication (OF), leurs avancements et identifier les alertes de dépassement de temps.

**🚀 Version Autonome** : Cette application fonctionne entièrement avec la base de données Excalibur ERP, sans dépendance aux fichiers Excel externes.

## Fonctionnalités

### 📊 Tableau de Bord Principal

- **Métriques en temps réel** : Total OF, OF en cours, avancement moyen production, alertes temps
- **Sélection d'intervalle** : Filtrage par dates de début et fin
- **Filtres avancés** : Par statut (C=En cours, T=Terminé, A=Arrêté)

### 📋 Gestion des Données

- **Affichage tabulaire** des ordres de fabrication
- **Filtres supplémentaires** : Par produit, par alerte, nombre d'enregistrements
- **Export CSV** des données filtrées
- **Calculs SQL natifs** :
  - Avancement Production = Cumul Entrées / Quantité Demandée
  - Avancement Temps = Cumul Temps Passés / Durée Prévue
  - Alertes automatiques si Avancement Temps > 100%
  - Efficacité = Temps Prévu / Temps Passé
  - Temps unitaires basés sur l'historique

### 📈 Analyses Graphiques

- **Vue d'ensemble** : Répartition des statuts et distribution des avancements
- **Scatter Plot** : Avancement Production vs Temps avec ligne de référence
- **Analyse des alertes** : Identification des OF en retard
- **Top 10 des retards** : Visualisation des OF les plus en retard

### 📄 Rapports

- **Rapport de synthèse** automatique avec statistiques clés
- **Export du rapport** en format texte
- **Identification des OF critiques**
- **Analyse par famille technique**
- **Recommandations automatiques**

### ⚙️ Analyse de Charge de Travail (Nouveau)

- **Calcul dynamique** de la charge par secteur
- **Taux de charge** en temps réel
- **Répartition des ressources** par qualification
- **Visualisation graphique** des charges

### 📋 Gestion du Backlog (Nouveau)

- **Identification automatique** des OF en retard
- **Système de priorités** (Urgent/Prioritaire/Normal)
- **Calcul du temps restant** estimé
- **Filtrage par client** et priorité

### 👥 Gestion du Personnel (Nouveau)

- **Liste du personnel actif** avec qualifications
- **Coefficients d'efficacité** par qualification
- **Analyse des compétences** disponibles
- **Répartition des qualifications**

## Installation et Lancement

### Méthode 1 : Script automatique (Recommandé)

```bash
python run_app.py
```

### Méthode 2 : Installation manuelle

```bash
# Installer les dépendances
pip install -r requirements.txt

# Lancer l'application
streamlit run app_suivi_production.py
```

## Configuration

### Base de Données (Seule source de données)

L'application se connecte automatiquement à la base de données Excalibur avec les paramètres :

- **Serveur** : *************:2638
- **Base** : excalib
- **Utilisateur** : gpao
- **Mot de passe** : flat

### Tables Utilisées

L'application accède directement aux tables suivantes :

- **OF_DA** : Ordres de fabrication principaux
- **HISTO_OF_DA** : Historique des OF pour calculs de temps unitaires
- **SALARIES** : Personnel actif et qualifications
- **Autres tables** : Selon les besoins de jointure

## Structure des Données

### Colonnes Principales

- **NUMERO_OFDA** : Numéro de l'ordre de fabrication
- **PRODUIT** : Référence du produit
- **STATUT** : Statut de l'OF (C/T/A)
- **LANCEMENT_AU_PLUS_TARD** : Date de lancement prévue
- **QUANTITE_DEMANDEE** : Quantité à produire
- **CUMUL_ENTREES** : Quantité déjà produite
- **DUREE_PREVUE** : Temps de production prévu
- **CUMUL_TEMPS_PASSES** : Temps déjà passé

### Calculs Automatiques

- **Avancement_PROD** : Pourcentage de production réalisée
- **Avancement_temps** : Pourcentage de temps consommé
- **Alerte_temps** : Indicateur de dépassement (True/False)
- **SEMAINE** : Semaine de lancement calculée automatiquement

## Utilisation

### 1. Sélection des Critères

- Choisir les dates de début et fin dans la barre latérale
- Sélectionner le statut des OF à analyser
- Cliquer sur "🔍 Rechercher"

### 2. Analyse des Données

- **Onglet Données** : Consulter le tableau détaillé avec filtres
- **Onglet Graphiques** : Visualiser les analyses graphiques
- **Onglet Rapport** : Lire le rapport de synthèse

### 3. Export des Résultats

- **CSV** : Télécharger les données filtrées
- **Rapport** : Télécharger le rapport de synthèse

## Indicateurs Clés

### 🟢 Statuts Normaux

- Avancement temps ≤ 100%
- Production en cours normale

### 🔴 Alertes

- ⚠️ Avancement temps > 100% (dépassement)
- OF en retard de production

### 📊 Métriques de Performance

- **Taux de respect des délais** : % OF sans dépassement temps
- **Efficacité production** : Ratio avancement production/temps
- **Charge de travail** : Répartition des OF par statut

## Dépendances

- **Streamlit** : Interface web
- **Pandas** : Manipulation des données
- **Plotly** : Graphiques interactifs
- **SQLAnyDB** : Connexion base de données
- **OpenPyXL** : Lecture fichiers Excel

## Support

Pour toute question ou problème :

1. Vérifier la connexion à la base de données
2. S'assurer que les fichiers Excel sont présents
3. Vérifier les dépendances installées

## Évolutions Futures

- Alertes en temps réel
- Notifications par email
- Intégration avec d'autres modules ERP
- Analyses prédictives
- Dashboard mobile
